const axios = require('axios');
const io = require('socket.io-client');

// Configuration
const API_GATEWAY_URL = 'http://localhost:3000';
const AUTH_SERVICE_URL = 'http://localhost:3001';
const NOTIFICATION_SERVICE_URL = 'http://localhost:3005';

// Generate random email for each test run
function generateRandomEmail() {
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 10000);
  return `testuser_${timestamp}_${randomNum}@example.com`;
}

// Test user data with random email
let testUser = {
  email: generateRandomEmail(),
  password: 'password123'
};

// Function to regenerate test user with new random email and username
function regenerateTestUser() {
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 10000);

  testUser = {
    email: generateRandomEmail(),
    password: 'password123'
  };

  // Also update profile data with random username (alphanumeric only)
  profileData = {
    username: `testuser${timestamp}${randomNum}`,
    full_name: 'Test User',
    school_id: 1,
    date_of_birth: '1995-01-01',
    gender: 'male'
  };

  console.log(`Generated new test user email: ${testUser.email}`);
  console.log(`Generated new username: ${profileData.username}`);
}

// Profile data with random username (will be updated in regenerateTestUser)
let profileData = {
  username: 'testuser',
  full_name: 'Test User',
  school_id: 1, // Use school_id for structured data
  // school_origin: 'SMA Manual Input', // Use this for manual text input
  date_of_birth: '1995-01-01',
  gender: 'male'
};

// Assessment data for WebSocket flow testing
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30,
    openness: 80
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 65,
    perseverance: 70,
    honesty: 75,
    zest: 60,
    love: 55,
    kindness: 68,
    socialIntelligence: 72,
    teamwork: 65,
    fairness: 70,
    leadership: 60,
    forgiveness: 55,
    humility: 50,
    prudence: 65,
    selfRegulation: 70,
    appreciationOfBeauty: 75,
    gratitude: 80,
    hope: 70,
    humor: 65,
    spirituality: 45
  }
};

let authToken = '';
let socket = null;
let jobId = '';
let userId = '';
let jobCompletionPromise = null;
let jobCompletionResolve = null;
let assessmentSubmissionTime = null;

// For mass testing - track submission times per user
let userAssessmentTimes = new Map(); // userIndex -> submissionTime

// Helper function to make API calls
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);

    // Ensure clean response object
    const cleanResponse = {
      success: true,
      status: response.status,
      data: response.data
    };

    return cleanResponse;
  } catch (error) {
    const cleanError = {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message }
    };

    return cleanError;
  }
}

// Helper function to format and display API response according to EX_API_DOCS.md format
function displayResponse(title, result, showFullResponse = false) {
  console.log(`\n${title}`);
  console.log('Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);

  if (result.success && result.data) {
    // Display according to standard API format from EX_API_DOCS.md
    if (result.data.success !== undefined) {
      console.log('API Success:', result.data.success ? '✅' : '❌');
    }

    if (result.data.message) {
      console.log('Message:', result.data.message);
    }

    if (result.data.data) {
      console.log('Response Data:');

      // Format specific data types nicely
      const responseData = result.data.data;

      if (responseData.user) {
        console.log('  User Info:');
        console.log(`    ID: ${responseData.user.id || 'N/A'}`);
        console.log(`    Email: ${responseData.user.email || 'N/A'}`);
        console.log(`    Username: ${responseData.user.username || 'N/A'}`);
        console.log(`    User Type: ${responseData.user.user_type || 'N/A'}`);
        console.log(`    Active: ${responseData.user.is_active || 'N/A'}`);
        console.log(`    Token Balance: ${responseData.user.token_balance || 'N/A'}`);

        if (responseData.user.profile) {
          console.log('  Profile:');
          console.log(`    Full Name: ${responseData.user.profile.full_name || 'N/A'}`);
          console.log(`    Date of Birth: ${responseData.user.profile.date_of_birth || 'N/A'}`);
          console.log(`    Gender: ${responseData.user.profile.gender || 'N/A'}`);

          if (responseData.user.profile.school) {
            console.log(`    School: ${responseData.user.profile.school.name || 'N/A'}`);
            console.log(`    City: ${responseData.user.profile.school.city || 'N/A'}`);
            console.log(`    Province: ${responseData.user.profile.school.province || 'N/A'}`);
          }
        }
      }

      if (responseData.token) {
        console.log(`  Token: ${responseData.token.substring(0, 20)}...`);
      }

      if (responseData.jobId) {
        console.log(`  Job ID: ${responseData.jobId}`);
      }

      if (responseData.status) {
        console.log(`  Status: ${responseData.status}`);
      }

      if (responseData.estimatedProcessingTime) {
        console.log(`  Estimated Processing Time: ${responseData.estimatedProcessingTime}`);
      }

      if (responseData.queuePosition) {
        console.log(`  Queue Position: ${responseData.queuePosition}`);
      }

      if (responseData.tokenCost) {
        console.log(`  Token Cost: ${responseData.tokenCost}`);
      }

      if (responseData.remainingTokens !== undefined) {
        console.log(`  Remaining Tokens: ${responseData.remainingTokens}`);
      }
    }

    // Show full response only in debug mode or when explicitly requested
    if (showFullResponse || process.env.DEBUG_RESPONSE === 'true') {
      console.log('\n=== FULL RESPONSE (DEBUG) ===');
      console.log(JSON.stringify(result.data, null, 2));
      console.log('=== END DEBUG ===');
    }
  } else {
    // Error response
    console.log('Error Details:');

    // Handle different error response formats
    if (result.data) {
      if (result.data.message) {
        console.log(`  Message: ${result.data.message}`);
      }
      if (result.data.error) {
        if (typeof result.data.error === 'string') {
          console.log(`  Error: ${result.data.error}`);
        } else {
          console.log(`  Error: ${JSON.stringify(result.data.error, null, 2)}`);
        }
      }
      if (result.data.details) {
        console.log(`  Details: ${JSON.stringify(result.data.details, null, 2)}`);
      }

      // If no specific error fields, show the whole data object
      if (!result.data.message && !result.data.error && !result.data.details) {
        console.log(`  Response: ${JSON.stringify(result.data, null, 2)}`);
      }
    }

    // Show full error response for debugging
    if (process.env.DEBUG_RESPONSE === 'true') {
      console.log('\n=== FULL ERROR RESPONSE (DEBUG) ===');
      console.log(JSON.stringify(result.data, null, 2));
      console.log('=== END DEBUG ===');
    }
  }
}



// Test functions
async function testHealthCheck() {
  console.log('\n=== 1. HEALTH CHECK ===');

  const services = [
    { name: 'API Gateway', url: `${API_GATEWAY_URL}/health` },
    { name: 'Auth Service', url: `${API_GATEWAY_URL}/api/auth/health` },
    { name: 'Assessment Service', url: `${API_GATEWAY_URL}/api/assessment/health` },
    { name: 'Archive Service', url: `${API_GATEWAY_URL}/api/archive/health` },
    { name: 'Notification Service', url: `${NOTIFICATION_SERVICE_URL}/health` }
  ];

  let allHealthy = true;

  for (const service of services) {
    console.log(`\nChecking ${service.name}...`);
    const result = await makeRequest('GET', service.url);
    const status = result.success ? '✅ HEALTHY' : '❌ UNHEALTHY';
    console.log(`${service.name}: ${status}`);

    if (result.success && result.data) {
      // Display health info according to EX_API_DOCS.md format
      if (result.data.status) {
        console.log(`  Status: ${result.data.status}`);
      }
      if (result.data.service) {
        console.log(`  Service: ${result.data.service}`);
      }
      if (result.data.version) {
        console.log(`  Version: ${result.data.version}`);
      }
      if (result.data.uptime) {
        console.log(`  Uptime: ${result.data.uptime}s`);
      }
    } else {
      allHealthy = false;
      console.log('  Error Details:');
      if (result.data.message) {
        console.log(`    Message: ${result.data.message}`);
      }
      if (result.data.error) {
        console.log(`    Error: ${result.data.error}`);
      }
    }
  }

  console.log(`\n=== HEALTH CHECK SUMMARY ===`);
  console.log(`Overall Status: ${allHealthy ? '✅ ALL SERVICES HEALTHY' : '❌ SOME SERVICES UNHEALTHY'}`);

  return allHealthy;
}

async function testRegister() {
  console.log('\n=== 2. USER REGISTRATION ===');

  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, testUser);

  displayResponse('Registration Result:', result);

  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    console.log('\n✅ Auth token saved for subsequent requests');
  }

  return result.success;
}

async function testLogin() {
  console.log('\n=== 3. USER LOGIN ===');

  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, testUser);

  displayResponse('Login Result:', result);

  if (result.success && result.data.data?.token) {
    authToken = result.data.data.token;
    userId = result.data.data.user.id;
    console.log('\n✅ Auth token updated from login response');
    console.log(`✅ User ID saved: ${userId}`);
  }

  return result.success;
}

async function testGetProfile() {
  console.log('\n=== 5. GET USER PROFILE ===');

  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }

  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });

  displayResponse('Get Profile Result:', result);

  return result.success;
}

async function testUpdateProfile() {
  console.log('\n=== 6. UPDATE USER PROFILE ===');

  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }

  const result = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, profileData, {
    'Authorization': `Bearer ${authToken}`
  });

  displayResponse('Update Profile Result:', result);

  return result.success;
}

async function testDeleteProfile() {
  console.log('\n=== 11. DELETE USER PROFILE ===');

  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }

  const result = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${authToken}`
  });

  displayResponse('Delete Profile Result:', result);

  return result.success;
}

// WebSocket Flow Testing Functions
async function testConnectWebSocket() {
  console.log('\n=== 4. WEBSOCKET CONNECTION ===');

  return new Promise((resolve) => {
    console.log(`Connecting to WebSocket at ${NOTIFICATION_SERVICE_URL}...`);

    socket = io(NOTIFICATION_SERVICE_URL, {
      transports: ['websocket'],
      timeout: 10000
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      console.log(`Socket ID: ${socket.id}`);

      // Authenticate the socket
      console.log('Authenticating WebSocket...');
      socket.emit('authenticate', { token: authToken });
    });

    socket.on('authenticated', (data) => {
      console.log('✅ WebSocket authenticated');
      console.log('Authentication Details:');
      if (data.userId) {
        console.log(`  User ID: ${data.userId}`);
      }
      if (data.message) {
        console.log(`  Message: ${data.message}`);
      }
      if (data.timestamp) {
        console.log(`  Timestamp: ${data.timestamp}`);
      }

      // Show full auth data only in debug mode
      if (process.env.DEBUG_RESPONSE === 'true') {
        console.log('\n=== FULL AUTH DATA (DEBUG) ===');
        console.log(JSON.stringify(data, null, 2));
        console.log('=== END DEBUG ===');
      }
      resolve(true);
    });

    socket.on('auth_error', (error) => {
      console.log('❌ WebSocket authentication failed');
      console.log('Error Details:');
      if (error.message) {
        console.log(`  Message: ${error.message}`);
      }
      if (error.code) {
        console.log(`  Code: ${error.code}`);
      }

      // Show full error only in debug mode
      if (process.env.DEBUG_RESPONSE === 'true') {
        console.log('\n=== FULL ERROR (DEBUG) ===');
        console.log(JSON.stringify(error, null, 2));
        console.log('=== END DEBUG ===');
      }
      resolve(false);
    });

    socket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection failed');
      console.log('Error:', error.message);
      resolve(false);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
    });

    // Set up notification listeners
    socket.on('analysis-complete', (data) => {
      const notificationTime = Date.now();
      console.log('\n🎉 ANALYSIS COMPLETE NOTIFICATION RECEIVED!');

      // Calculate and display processing time
      if (assessmentSubmissionTime) {
        const processingTime = notificationTime - assessmentSubmissionTime;
        console.log(`⏱️  Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`);
        console.log(`   Submitted at: ${new Date(assessmentSubmissionTime).toISOString()}`);
        console.log(`   Completed at: ${new Date(notificationTime).toISOString()}`);
      }

      console.log('Notification Details:');
      if (data.jobId) {
        console.log(`  Job ID: ${data.jobId}`);
      }
      if (data.resultId) {
        console.log(`  Result ID: ${data.resultId}`);
      }
      if (data.userId) {
        console.log(`  User ID: ${data.userId}`);
      }
      if (data.message) {
        console.log(`  Message: ${data.message}`);
      }
      if (data.timestamp) {
        console.log(`  Timestamp: ${data.timestamp}`);
      }

      // Show full notification data only in debug mode
      if (process.env.DEBUG_RESPONSE === 'true') {
        console.log('\n=== FULL NOTIFICATION DATA (DEBUG) ===');
        console.log(JSON.stringify(data, null, 2));
        console.log('=== END DEBUG ===');
      }

      // Resolve job completion promise if waiting
      if (jobCompletionResolve && data.jobId === jobId) {
        jobCompletionResolve({ success: true, data });
      }
    });

    socket.on('analysis-failed', (data) => {
      const notificationTime = Date.now();
      console.log('\n❌ ANALYSIS FAILED NOTIFICATION RECEIVED!');

      // Calculate and display processing time even for failures
      if (assessmentSubmissionTime) {
        const processingTime = notificationTime - assessmentSubmissionTime;
        console.log(`⏱️  Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`);
        console.log(`   Submitted at: ${new Date(assessmentSubmissionTime).toISOString()}`);
        console.log(`   Failed at: ${new Date(notificationTime).toISOString()}`);
      }

      console.log('Failure Details:');
      if (data.jobId) {
        console.log(`  Job ID: ${data.jobId}`);
      }
      if (data.error) {
        console.log(`  Error: ${data.error}`);
      }
      if (data.userId) {
        console.log(`  User ID: ${data.userId}`);
      }
      if (data.message) {
        console.log(`  Message: ${data.message}`);
      }
      if (data.timestamp) {
        console.log(`  Timestamp: ${data.timestamp}`);
      }

      // Show full notification data only in debug mode
      if (process.env.DEBUG_RESPONSE === 'true') {
        console.log('\n=== FULL NOTIFICATION DATA (DEBUG) ===');
        console.log(JSON.stringify(data, null, 2));
        console.log('=== END DEBUG ===');
      }

      // Resolve job completion promise if waiting
      if (jobCompletionResolve && data.jobId === jobId) {
        jobCompletionResolve({ success: false, data });
      }
    });

    // Timeout after 10 seconds
    setTimeout(() => {
      if (!socket.connected) {
        console.log('❌ WebSocket connection timeout');
        resolve(false);
      }
    }, 10000);
  });
}

async function testSubmitAssessment() {
  console.log('\n=== 8. SUBMIT ASSESSMENT ===');

  if (!authToken) {
    console.log('❌ No auth token available');
    return false;
  }

  console.log('Submitting assessment data...');

  // Record submission time for timing measurement
  assessmentSubmissionTime = Date.now();
  console.log(`⏱️  Assessment submission started at: ${new Date(assessmentSubmissionTime).toISOString()}`);

  const result = await makeRequest('POST', `${API_GATEWAY_URL}/api/assessment/submit`, assessmentData, {
    'Authorization': `Bearer ${authToken}`
  });

  displayResponse('Submit Assessment Result:', result);

  if (result.success && result.data.data?.jobId) {
    jobId = result.data.data.jobId;
    console.log(`\n✅ Job ID saved: ${jobId}`);
    return true;
  }

  return false;
}

async function testMonitorJobStatus() {
  console.log('\n=== 9. MONITOR JOB STATUS ===');

  if (!jobId) {
    console.log('❌ No job ID available');
    return false;
  }

  if (!socket || !socket.connected) {
    console.log('❌ WebSocket not connected');
    return false;
  }

  console.log(`Monitoring job: ${jobId}`);
  console.log('Waiting for WebSocket notifications...');
  console.log('📡 No more polling! Using real-time notifications via WebSocket.');

  // Create a promise that resolves when job completion notification is received
  jobCompletionPromise = new Promise((resolve) => {
    jobCompletionResolve = resolve;

    // Set a timeout for 5 minutes
    setTimeout(() => {
      if (jobCompletionResolve) {
        console.log('⏰ Timeout reached while waiting for job completion notification');
        resolve({ success: false, timeout: true });
      }
    }, 5 * 60 * 1000); // 5 minutes
  });

  try {
    const result = await jobCompletionPromise;

    // Clean up
    jobCompletionResolve = null;
    jobCompletionPromise = null;

    if (result.timeout) {
      return false;
    }

    if (result.success) {
      console.log('✅ Job completed successfully via notification!');
      if (result.data.resultId) {
        console.log(`Result ID: ${result.data.resultId}`);
      }
      return true;
    } else {
      console.log('❌ Job failed via notification!');
      if (result.data.error) {
        console.log(`Error: ${result.data.error}`);
      }
      return false;
    }
  } catch (error) {
    console.log('❌ Error while waiting for job completion:', error.message);
    return false;
  }
}

async function testGetJobFromArchive() {
  console.log('\n=== 10. GET JOB FROM ARCHIVE ===');

  if (!jobId) {
    console.log('❌ No job ID available');
    return false;
  }

  console.log(`Getting job details for: ${jobId}`);
  const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
    'Authorization': `Bearer ${authToken}`
  });

  // Custom display for job details to match EX_API_DOCS.md format
  console.log('\nGet Job Result:');
  console.log('Status:', result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log('HTTP Status:', result.status);

  if (result.success && result.data) {
    if (result.data.success !== undefined) {
      console.log('API Success:', result.data.success ? '✅' : '❌');
    }

    if (result.data.data) {
      console.log('Job Details:');
      console.log(`  ID: ${result.data.data.id || 'N/A'}`);
      console.log(`  Job ID: ${result.data.data.job_id || 'N/A'}`);
      console.log(`  User ID: ${result.data.data.user_id || 'N/A'}`);
      console.log(`  Status: ${result.data.data.status || 'N/A'}`);
      console.log(`  Priority: ${result.data.data.priority || 'N/A'}`);
      console.log(`  Retry Count: ${result.data.data.retry_count || 'N/A'}`);
      console.log(`  Max Retries: ${result.data.data.max_retries || 'N/A'}`);
      console.log(`  Result ID: ${result.data.data.result_id || 'N/A'}`);
      console.log(`  Created: ${result.data.data.created_at || 'N/A'}`);
      console.log(`  Updated: ${result.data.data.updated_at || 'N/A'}`);
    }

    // Only show full response in debug mode
    if (process.env.DEBUG_RESPONSE === 'true') {
      console.log('\n=== FULL RESPONSE (DEBUG) ===');
      console.log(JSON.stringify(result.data, null, 2));
      console.log('=== END DEBUG ===');
    }
  } else {
    console.log('Error Details:');
    if (result.data.message) {
      console.log(`  Message: ${result.data.message}`);
    }
    if (result.data.error) {
      console.log(`  Error: ${result.data.error}`);
    }

    if (process.env.DEBUG_RESPONSE === 'true') {
      console.log('\n=== FULL ERROR RESPONSE (DEBUG) ===');
      console.log(JSON.stringify(result.data, null, 2));
      console.log('=== END DEBUG ===');
    }
  }

  return result.success;
}

function cleanup() {
  console.log('\n=== 🧹 CLEANUP ===');

  // Clean up job completion tracking
  if (jobCompletionResolve) {
    jobCompletionResolve({ success: false, cleanup: true });
    jobCompletionResolve = null;
    jobCompletionPromise = null;
  }

  if (socket) {
    console.log('Disconnecting WebSocket...');
    socket.disconnect();
    socket = null;
  }

  // Reset variables
  authToken = '';
  jobId = '';
  userId = '';
  assessmentSubmissionTime = null;
  userAssessmentTimes.clear();

  console.log('Cleanup completed');
}

// WebSocket Flow Test Runner
async function runWebSocketFlow() {
  console.log('🚀 Starting ATMA WebSocket Flow Test');
  console.log('====================================');

  try {
    // Generate new random email for this test run
    regenerateTestUser();

    // Check all services health
    const servicesHealthy = await testHealthCheck();
    if (!servicesHealthy) {
      console.log('\n⚠️ Some services are not healthy. Please start all services before testing.');
      return;
    }

    // Authenticate user (register first, then login if registration fails)
    const registerSuccess = await testRegister();
    if (!registerSuccess) {
      console.log('\n⚠️ Registration failed, trying login instead...');
      const loginSuccess = await testLogin();
      if (!loginSuccess) {
        console.log('\n❌ Authentication failed. Cannot proceed with WebSocket flow.');
        return;
      }
    }

    // Connect to WebSocket immediately after login
    const wsConnected = await testConnectWebSocket();
    if (!wsConnected) {
      console.log('\n❌ WebSocket connection failed. Cannot proceed with flow.');
      return;
    }

    // Get and update profile to ensure user is properly set up
    await testGetProfile();
    const profileUpdateSuccess = await testUpdateProfile();

    // Get profile again to see the updated data
    console.log('\n=== 7. GET UPDATED PROFILE ===');
    const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    displayResponse('Get Updated Profile Result:', result);

    if (!profileUpdateSuccess) {
      console.log('\n⚠️ Profile update failed, but continuing with WebSocket test...');
    }

    // Submit assessment
    const submitSuccess = await testSubmitAssessment();
    if (!submitSuccess) {
      cleanup();
      return;
    }

    // Monitor job status and wait for notifications
    const jobCompleted = await testMonitorJobStatus();

    // Get final job details from archive
    await testGetJobFromArchive();

    // Delete profile at the end (optional - can be skipped with --keep-user flag)
    const args = process.argv.slice(2);
    if (!args.includes('--keep-user')) {
      console.log('\n=== 12. CLEANUP USER PROFILE ===');
      if (profileUpdateSuccess) {
        await testDeleteProfile();
      } else {
        console.log('⚠️ Skipping profile deletion since profile was not created successfully');
      }
    } else {
      console.log('\n=== 12. KEEPING USER PROFILE ===');
      console.log('User profile kept for future testing (--keep-user flag used)');
    }

    console.log('\n🎉 WebSocket Flow Test Completed!');
    console.log('==================================');

    if (jobCompleted) {
      console.log('✅ Test Result: SUCCESS - Job completed and notifications received');
    } else {
      console.log('⚠️ Test Result: PARTIAL - Job may still be processing');
    }

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  } finally {
    cleanup();
  }
}

// Main test runner (original auth flow)
async function runTests() {
  console.log('🚀 Starting ATMA User Flow Tests');
  console.log('=====================================');

  try {
    // Generate new random email for this test run
    regenerateTestUser();

    // Run all tests in sequence
    await testHealthCheck();

    const registerSuccess = await testRegister();
    if (!registerSuccess) {
      console.log('\n⚠️ Registration failed, trying login instead...');
      await testLogin();
    }

    await testGetProfile();
    await testUpdateProfile();

    // Get profile again to see the updated data
    console.log('\n=== 4b. GET UPDATED PROFILE ===');
    const result = await makeRequest('GET', `${API_GATEWAY_URL}/api/auth/profile`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    displayResponse('Get Updated Profile Result:', result);

    await testDeleteProfile();

    console.log('\n🎉 All tests completed!');
    console.log('=====================================');

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  // Check command line arguments
  const args = process.argv.slice(2);

  if (args.includes('--end-to-end') || args.includes('-e')) {
    console.log('Running Mass End-to-End Test...\n');

    // Parse end-to-end test options
    const userCountArg = args.find(arg => arg.startsWith('--users='));
    const userCount = userCountArg ? parseInt(userCountArg.split('=')[1]) : 50;

    const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
    const batchSize = batchSizeArg ? parseInt(batchSizeArg.split('=')[1]) : 10;

    const batchDelayArg = args.find(arg => arg.startsWith('--batch-delay='));
    const batchDelay = batchDelayArg ? parseInt(batchDelayArg.split('=')[1]) : 3000;

    const userDelayArg = args.find(arg => arg.startsWith('--user-delay='));
    const userDelay = userDelayArg ? parseInt(userDelayArg.split('=')[1]) : 200;

    const wsDelayArg = args.find(arg => arg.startsWith('--ws-delay='));
    const wsDelay = wsDelayArg ? parseInt(wsDelayArg.split('=')[1]) : 300;

    const jobTimeoutArg = args.find(arg => arg.startsWith('--job-timeout='));
    const jobTimeout = jobTimeoutArg ? parseInt(jobTimeoutArg.split('=')[1]) : 300000;

    const enableWebSocket = !args.includes('--no-websocket');
    const cleanupUsers = !args.includes('--no-cleanup');

    // High-performance mode
    const highPerformanceMode = args.includes('--high-performance') || args.includes('--hp');
    if (highPerformanceMode) {
      console.log('🚀 HIGH-PERFORMANCE MODE ENABLED');
    }

    const options = {
      batchSize,
      batchDelay,
      userDelay,
      wsDelay,
      enableWebSocket,
      cleanupUsers,
      highPerformance: highPerformanceMode,
      skipProfile: highPerformanceMode,
      jobTimeout
    };

    console.log(`Configuration:`);
    console.log(`  Users: ${userCount}`);
    console.log(`  Batch Size: ${options.batchSize}`);
    console.log(`  Batch Delay: ${options.batchDelay}ms`);
    console.log(`  User Delay: ${options.userDelay}ms`);
    console.log(`  WebSocket Delay: ${options.wsDelay}ms`);
    console.log(`  Job Timeout: ${options.jobTimeout / 1000}s`);
    console.log(`  WebSocket Enabled: ${enableWebSocket}`);
    console.log(`  Cleanup Users: ${cleanupUsers}`);
    console.log(`  High Performance Mode: ${highPerformanceMode}`);
    console.log('');

    runMassEndToEndTest(userCount, options);
  } else if (args.includes('--mass-login') || args.includes('-m')) {
    console.log('Running Mass Login Test...\n');

    // Parse mass login options
    const userCountArg = args.find(arg => arg.startsWith('--users='));
    const userCount = userCountArg ? parseInt(userCountArg.split('=')[1]) : 250;

    const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
    const batchSize = batchSizeArg ? parseInt(batchSizeArg.split('=')[1]) : 20; // Increased from 10 to 20

    const batchDelayArg = args.find(arg => arg.startsWith('--batch-delay='));
    const batchDelay = batchDelayArg ? parseInt(batchDelayArg.split('=')[1]) : 1000; // Reduced from 2000 to 1000

    const userDelayArg = args.find(arg => arg.startsWith('--user-delay='));
    const userDelay = userDelayArg ? parseInt(userDelayArg.split('=')[1]) : 50; // Reduced from 100 to 50

    const wsDelayArg = args.find(arg => arg.startsWith('--ws-delay='));
    const wsDelay = wsDelayArg ? parseInt(wsDelayArg.split('=')[1]) : 100; // Reduced from 200 to 100

    const enableWebSocket = !args.includes('--no-websocket');
    const cleanupUsers = !args.includes('--no-cleanup');

    // High-performance mode
    const highPerformanceMode = args.includes('--high-performance') || args.includes('--hp');
    if (highPerformanceMode) {
      console.log('🚀 HIGH-PERFORMANCE MODE ENABLED');
      // Override settings for maximum speed
      options.batchSize = Math.max(batchSize, 50);
      options.batchDelay = Math.min(batchDelay, 500);
      options.userDelay = Math.min(userDelay, 25);
      options.wsDelay = Math.min(wsDelay, 50);
    }

    const options = {
      batchSize,
      batchDelay,
      userDelay,
      wsDelay,
      enableWebSocket,
      cleanupUsers,
      highPerformance: highPerformanceMode,
      skipProfile: highPerformanceMode // Skip profile updates in high-performance mode
    };

    console.log(`Configuration:`);
    console.log(`  Users: ${userCount}`);
    console.log(`  Batch Size: ${options.batchSize}`);
    console.log(`  Batch Delay: ${options.batchDelay}ms`);
    console.log(`  User Delay: ${options.userDelay}ms`);
    console.log(`  WebSocket Delay: ${options.wsDelay}ms`);
    console.log(`  WebSocket Enabled: ${enableWebSocket}`);
    console.log(`  Cleanup Users: ${cleanupUsers}`);
    console.log(`  High Performance Mode: ${highPerformanceMode}`);
    console.log('');

    runMassLoginTest(userCount, options);
  } else if (args.includes('--websocket') || args.includes('-w')) {
    console.log('Running WebSocket Flow Test...\n');
    runWebSocketFlow();
  } else if (args.includes('--help') || args.includes('-h')) {
    console.log('ATMA Testing Options:');
    console.log('  node test-user-flow.js                    - Run basic auth flow tests');
    console.log('  node test-user-flow.js --websocket        - Run WebSocket flow test');
    console.log('  node test-user-flow.js -w                 - Run WebSocket flow test (short)');
    console.log('  node test-user-flow.js --websocket --keep-user - Run WebSocket test, keep user profile');
    console.log('  node test-user-flow.js --end-to-end       - Run end-to-end test (50 users)');
    console.log('  node test-user-flow.js -e                 - Run end-to-end test (short)');
    console.log('  node test-user-flow.js --mass-login       - Run mass login test (250 users)');
    console.log('  node test-user-flow.js -m                 - Run mass login test (short)');
    console.log('  node test-user-flow.js --help             - Show this help');
    console.log('');
    console.log('End-to-End Test Options:');
    console.log('  --users=N                                 - Number of users to test (default: 50)');
    console.log('  --batch-size=N                            - Users per batch (default: 10)');
    console.log('  --batch-delay=N                           - Delay between batches in ms (default: 3000)');
    console.log('  --user-delay=N                            - Delay between users in batch in ms (default: 200)');
    console.log('  --ws-delay=N                              - Delay between WebSocket connections in ms (default: 300)');
    console.log('  --job-timeout=N                           - Job completion timeout in ms (default: 300000)');
    console.log('  --high-performance, --hp                  - Enable high-performance mode (max speed)');
    console.log('  --no-websocket                            - Skip WebSocket testing');
    console.log('  --no-cleanup                              - Skip user cleanup after test');
    console.log('');
    console.log('Mass Login Test Options:');
    console.log('  --users=N                                 - Number of users to test (default: 250)');
    console.log('  --batch-size=N                            - Users per batch (default: 20)');
    console.log('  --batch-delay=N                           - Delay between batches in ms (default: 1000)');
    console.log('  --user-delay=N                            - Delay between users in batch in ms (default: 50)');
    console.log('  --ws-delay=N                              - Delay between WebSocket connections in ms (default: 100)');
    console.log('  --high-performance, --hp                  - Enable high-performance mode (max speed)');
    console.log('  --no-websocket                            - Skip WebSocket testing');
    console.log('  --no-cleanup                              - Skip user cleanup after test');
    console.log('');
    console.log('Environment Variables:');
    console.log('  DEBUG_RESPONSE=true                       - Show full API responses (debug mode)');
    console.log('');
    console.log('Examples:');
    console.log('  # End-to-End Testing (Login + Submit + Get Result)');
    console.log('  node test-user-flow.js --end-to-end --users=10');
    console.log('  node test-user-flow.js -e --users=50 --high-performance');
    console.log('  node test-user-flow.js -e --users=100 --batch-size=5 --job-timeout=600000');
    console.log('');
    console.log('  # Mass Login Testing (Login + WebSocket only)');
    console.log('  node test-user-flow.js --mass-login --users=100 --batch-size=5');
    console.log('  node test-user-flow.js -m --users=50 --no-websocket');
    console.log('  node test-user-flow.js -m --users=1000 --high-performance');
    console.log('  node test-user-flow.js -m --users=500 --hp --batch-size=50');
    console.log('');
    console.log('  # Other Tests');
    console.log('  DEBUG_RESPONSE=true node test-user-flow.js --websocket');
    console.log('  node test-user-flow.js --websocket --keep-user');
  } else {
    console.log('Running basic auth flow tests...');
    console.log('Use --websocket or -w flag to run WebSocket flow test');
    console.log('Use --mass-login or -m flag to run mass login test\n');
    runTests();
  }
}

// Mass Login Test Functions
async function createTestUser(index) {
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 10000);

  return {
    email: `masstest_${timestamp}_${index}_${randomNum}@example.com`,
    password: 'password123',
    username: `massuser${timestamp}${index}${randomNum}`,
    full_name: `Mass Test User ${index}`,
    school_id: 1,
    date_of_birth: '1995-01-01',
    gender: 'male'
  };
}

async function loginSingleUser(userIndex, delay = 0, skipProfile = false) {
  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  const user = await createTestUser(userIndex);
  const startTime = Date.now();

  try {
    console.log(`[User ${userIndex}] Starting login process...`);

    // Parallel execution of register and login preparation
    const registerPromise = makeRequest('POST', `${API_GATEWAY_URL}/api/auth/register`, {
      email: user.email,
      password: user.password
    });

    const registerResult = await registerPromise;

    if (!registerResult.success) {
      console.log(`[User ${userIndex}] Registration failed:`, registerResult.data?.message || 'Unknown error');
      return { success: false, userIndex, error: 'Registration failed', user };
    }

    // Login user immediately after registration
    const loginResult = await makeRequest('POST', `${API_GATEWAY_URL}/api/auth/login`, {
      email: user.email,
      password: user.password
    });

    if (!loginResult.success) {
      console.log(`[User ${userIndex}] Login failed:`, loginResult.data?.message || 'Unknown error');
      return { success: false, userIndex, error: 'Login failed', user };
    }

    const authToken = loginResult.data.data?.token;
    const userId = loginResult.data.data?.user?.id;

    if (!authToken || !userId) {
      console.log(`[User ${userIndex}] Invalid login response - missing token or user ID`);
      return { success: false, userIndex, error: 'Invalid login response', user };
    }

    let profileResult = { success: true };

    // Skip profile update in high-performance mode to reduce load
    if (!skipProfile) {
      profileResult = await makeRequest('PUT', `${API_GATEWAY_URL}/api/auth/profile`, {
        username: user.username,
        full_name: user.full_name,
        school_id: user.school_id,
        date_of_birth: user.date_of_birth,
        gender: user.gender
      }, {
        'Authorization': `Bearer ${authToken}`
      });
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[User ${userIndex}] ✅ Login completed in ${duration}ms`);

    return {
      success: true,
      userIndex,
      user,
      authToken,
      userId,
      duration,
      profileUpdated: profileResult.success
    };

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[User ${userIndex}] ❌ Login failed after ${duration}ms:`, error.message);
    return { success: false, userIndex, error: error.message, user, duration };
  }
}

// Full End-to-End Test Functions
async function submitAssessmentForUser(userResult, delay = 0) {
  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  if (!userResult.success) {
    return { success: false, userIndex: userResult.userIndex, error: 'User login failed' };
  }

  const { userIndex, authToken, userId } = userResult;
  const startTime = Date.now();

  try {
    console.log(`[User ${userIndex}] Submitting assessment...`);

    // Record submission time for timing measurement
    const submissionTime = Date.now();
    userAssessmentTimes.set(userIndex, submissionTime);
    console.log(`[User ${userIndex}] ⏱️  Assessment submission started at: ${new Date(submissionTime).toISOString()}`);

    // Submit assessment
    const submitResult = await makeRequest('POST', `${API_GATEWAY_URL}/api/assessment/submit`, assessmentData, {
      'Authorization': `Bearer ${authToken}`
    });

    if (!submitResult.success) {
      console.log(`[User ${userIndex}] Assessment submission failed:`, submitResult.data?.message || 'Unknown error');
      return { success: false, userIndex, error: 'Assessment submission failed', userResult };
    }

    const jobId = submitResult.data.data?.jobId;
    if (!jobId) {
      console.log(`[User ${userIndex}] No job ID received from assessment submission`);
      return { success: false, userIndex, error: 'No job ID received', userResult };
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[User ${userIndex}] ✅ Assessment submitted in ${duration}ms, Job ID: ${jobId}`);

    return {
      success: true,
      userIndex,
      userId,
      authToken,
      jobId,
      duration,
      userResult
    };

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[User ${userIndex}] ❌ Assessment submission failed after ${duration}ms:`, error.message);
    return { success: false, userIndex, error: error.message, userResult, duration };
  }
}

async function connectWebSocketForUser(userResult, delay = 0) {
  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  if (!userResult.success) {
    return { success: false, userIndex: userResult.userIndex, error: 'User login failed' };
  }

  const { userIndex, authToken, userId } = userResult;
  const startTime = Date.now();

  return new Promise((resolve) => {
    console.log(`[User ${userIndex}] Connecting to WebSocket...`);

    const socket = io(NOTIFICATION_SERVICE_URL, {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true
    });

    let resolved = false;

    const resolveOnce = (result) => {
      if (!resolved) {
        resolved = true;
        resolve(result);
      }
    };

    socket.on('connect', () => {
      console.log(`[User ${userIndex}] WebSocket connected, authenticating...`);
      socket.emit('authenticate', { token: authToken });
    });

    socket.on('authenticated', (data) => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[User ${userIndex}] ✅ WebSocket authenticated in ${duration}ms`);

      // Set up notification listeners for this user
      socket.on('analysis-complete', (notificationData) => {
        const notificationTime = Date.now();
        console.log(`\n[User ${userIndex}] 🎉 ANALYSIS COMPLETE NOTIFICATION RECEIVED!`);

        // Calculate and display processing time
        const submissionTime = userAssessmentTimes.get(userIndex);
        if (submissionTime) {
          const processingTime = notificationTime - submissionTime;
          console.log(`[User ${userIndex}] ⏱️  Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`);
          console.log(`[User ${userIndex}]    Submitted at: ${new Date(submissionTime).toISOString()}`);
          console.log(`[User ${userIndex}]    Completed at: ${new Date(notificationTime).toISOString()}`);
        }

        if (notificationData.jobId) {
          console.log(`[User ${userIndex}]    Job ID: ${notificationData.jobId}`);
        }
        if (notificationData.resultId) {
          console.log(`[User ${userIndex}]    Result ID: ${notificationData.resultId}`);
        }
      });

      socket.on('analysis-failed', (notificationData) => {
        const notificationTime = Date.now();
        console.log(`\n[User ${userIndex}] ❌ ANALYSIS FAILED NOTIFICATION RECEIVED!`);

        // Calculate and display processing time even for failures
        const submissionTime = userAssessmentTimes.get(userIndex);
        if (submissionTime) {
          const processingTime = notificationTime - submissionTime;
          console.log(`[User ${userIndex}] ⏱️  Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`);
          console.log(`[User ${userIndex}]    Submitted at: ${new Date(submissionTime).toISOString()}`);
          console.log(`[User ${userIndex}]    Failed at: ${new Date(notificationTime).toISOString()}`);
        }

        if (notificationData.jobId) {
          console.log(`[User ${userIndex}]    Job ID: ${notificationData.jobId}`);
        }
        if (notificationData.error) {
          console.log(`[User ${userIndex}]    Error: ${notificationData.error}`);
        }
      });

      resolveOnce({
        success: true,
        userIndex,
        userId,
        socket,
        duration,
        authData: data
      });
    });

    socket.on('auth_error', (error) => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[User ${userIndex}] ❌ WebSocket auth failed after ${duration}ms:`, error.message);
      socket.disconnect();

      resolveOnce({
        success: false,
        userIndex,
        error: error.message,
        duration
      });
    });

    socket.on('connect_error', (error) => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[User ${userIndex}] ❌ WebSocket connection failed after ${duration}ms:`, error.message);

      resolveOnce({
        success: false,
        userIndex,
        error: error.message,
        duration
      });
    });

    // Timeout after 15 seconds
    setTimeout(() => {
      if (!resolved) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log(`[User ${userIndex}] ❌ WebSocket connection timeout after ${duration}ms`);
        socket.disconnect();

        resolveOnce({
          success: false,
          userIndex,
          error: 'Connection timeout',
          duration
        });
      }
    }, 15000);
  });
}

async function waitForJobCompletion(assessmentResult, timeout = 300000) {
  if (!assessmentResult.success) {
    return { success: false, userIndex: assessmentResult.userIndex, error: 'Assessment submission failed' };
  }

  const { userIndex, jobId, authToken } = assessmentResult;
  const startTime = Date.now();

  try {
    console.log(`[User ${userIndex}] Waiting for job completion: ${jobId}`);

    // Poll job status until completion or timeout
    while (Date.now() - startTime < timeout) {
      const statusResult = await makeRequest('GET', `${API_GATEWAY_URL}/api/archive/jobs/${jobId}`, null, {
        'Authorization': `Bearer ${authToken}`
      });

      if (statusResult.success && statusResult.data.data) {
        const job = statusResult.data.data;

        if (job.status === 'completed') {
          const endTime = Date.now();
          const duration = endTime - startTime;

          console.log(`[User ${userIndex}] ✅ Job completed in ${duration}ms, Result ID: ${job.result_id}`);

          return {
            success: true,
            userIndex,
            jobId,
            resultId: job.result_id,
            duration,
            assessmentResult
          };
        } else if (job.status === 'failed') {
          console.log(`[User ${userIndex}] ❌ Job failed: ${job.error_message || 'Unknown error'}`);
          return { success: false, userIndex, error: 'Job failed', assessmentResult };
        }
      }

      // Wait 2 seconds before next poll
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Timeout reached
    console.log(`[User ${userIndex}] ⏰ Job completion timeout after ${timeout}ms`);
    return { success: false, userIndex, error: 'Job completion timeout', assessmentResult };

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[User ${userIndex}] ❌ Error waiting for job completion after ${duration}ms:`, error.message);
    return { success: false, userIndex, error: error.message, assessmentResult, duration };
  }
}

async function runMassLoginTest(userCount = 250, options = {}) {
  console.log(`🚀 Starting Mass Login Test for ${userCount} users`);
  console.log('='.repeat(60));

  const {
    batchSize = 20,           // Increased default batch size
    batchDelay = 1000,        // Reduced default delay
    userDelay = 50,           // Reduced default delay
    enableWebSocket = true,   // Whether to test WebSocket connections
    wsDelay = 100,           // Reduced default delay
    cleanupUsers = true,      // Whether to cleanup users after test
    highPerformance = false,  // High-performance mode
    skipProfile = false       // Skip profile updates for speed
  } = options;

  const startTime = Date.now();
  const results = {
    total: userCount,
    loginSuccess: 0,
    loginFailed: 0,
    wsSuccess: 0,
    wsFailed: 0,
    errors: [],
    durations: {
      login: [],
      websocket: []
    }
  };

  // Check services health first
  console.log('\n📋 Checking services health...');
  const servicesHealthy = await testHealthCheck();
  if (!servicesHealthy) {
    console.log('\n❌ Some services are not healthy. Aborting mass test.');
    return results;
  }

  const allSockets = [];
  const successfulUsers = [];

  try {
    // Process users in batches
    for (let batchStart = 0; batchStart < userCount; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, userCount);
      const currentBatch = batchEnd - batchStart;

      console.log(`\n📦 Processing batch ${Math.floor(batchStart / batchSize) + 1}: Users ${batchStart + 1}-${batchEnd}`);

      // Login users in current batch
      const loginPromises = [];
      for (let i = batchStart; i < batchEnd; i++) {
        const delay = highPerformance ? 0 : (i - batchStart) * userDelay;
        loginPromises.push(loginSingleUser(i + 1, delay, skipProfile));
      }

      const loginResults = await Promise.all(loginPromises);

      // Process login results
      for (const result of loginResults) {
        if (result.success) {
          results.loginSuccess++;
          results.durations.login.push(result.duration);
          successfulUsers.push(result);
        } else {
          results.loginFailed++;
          results.errors.push({
            type: 'login',
            userIndex: result.userIndex,
            error: result.error
          });
        }
      }

      console.log(`   Login results: ${loginResults.filter(r => r.success).length}/${currentBatch} successful`);

      // Connect WebSockets for successful logins if enabled
      if (enableWebSocket && loginResults.some(r => r.success)) {
        console.log(`   Connecting WebSockets for successful logins...`);

        const wsPromises = [];
        const successfulLogins = loginResults.filter(r => r.success);

        for (let i = 0; i < successfulLogins.length; i++) {
          const delay = i * wsDelay;
          wsPromises.push(connectWebSocketForUser(successfulLogins[i], delay));
        }

        const wsResults = await Promise.all(wsPromises);

        // Process WebSocket results
        for (const result of wsResults) {
          if (result.success) {
            results.wsSuccess++;
            results.durations.websocket.push(result.duration);
            allSockets.push(result.socket);
          } else {
            results.wsFailed++;
            results.errors.push({
              type: 'websocket',
              userIndex: result.userIndex,
              error: result.error
            });
          }
        }

        console.log(`   WebSocket results: ${wsResults.filter(r => r.success).length}/${successfulLogins.length} successful`);
      }

      // Delay before next batch (except for last batch)
      if (batchEnd < userCount) {
        console.log(`   Waiting ${batchDelay}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    }

    // Show connection status
    if (enableWebSocket && allSockets.length > 0) {
      console.log(`\n📊 Checking notification service status...`);
      try {
        const statusResult = await makeRequest('GET', `${NOTIFICATION_SERVICE_URL}/debug/connections`);
        if (statusResult.success) {
          const summary = statusResult.data.summary;
          console.log(`   Total connections: ${summary.total}`);
          console.log(`   Authenticated users: ${summary.authenticated}`);
          console.log(`   Unique users: ${summary.users}`);
        }
      } catch (error) {
        console.log(`   Could not get connection status: ${error.message}`);
      }
    }

    // Keep connections alive for a short time to test stability
    if (allSockets.length > 0) {
      console.log(`\n⏱️  Keeping ${allSockets.length} connections alive for 30 seconds...`);
      await new Promise(resolve => setTimeout(resolve, 30000));
    }

  } catch (error) {
    console.error('\n💥 Mass test execution failed:', error.message);
    results.errors.push({
      type: 'execution',
      error: error.message
    });
  } finally {
    // Cleanup WebSocket connections
    if (allSockets.length > 0) {
      console.log(`\n🧹 Disconnecting ${allSockets.length} WebSocket connections...`);
      for (const socket of allSockets) {
        try {
          socket.disconnect();
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    }

    // Cleanup user accounts if requested
    if (cleanupUsers && successfulUsers.length > 0) {
      console.log(`\n🗑️  Cleaning up ${successfulUsers.length} user accounts...`);
      let cleanupCount = 0;

      for (const userResult of successfulUsers) {
        try {
          const deleteResult = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
            'Authorization': `Bearer ${userResult.authToken}`
          });
          if (deleteResult.success) {
            cleanupCount++;
          }
        } catch (error) {
          // Ignore cleanup errors
        }
      }

      console.log(`   Cleaned up ${cleanupCount}/${successfulUsers.length} user accounts`);
    }
  }

  // Calculate statistics
  const endTime = Date.now();
  const totalDuration = endTime - startTime;

  const avgLoginTime = results.durations.login.length > 0
    ? results.durations.login.reduce((a, b) => a + b, 0) / results.durations.login.length
    : 0;

  const avgWsTime = results.durations.websocket.length > 0
    ? results.durations.websocket.reduce((a, b) => a + b, 0) / results.durations.websocket.length
    : 0;

  // Display final results
  console.log('\n' + '='.repeat(60));
  console.log('🎉 MASS LOGIN TEST COMPLETED!');
  console.log('='.repeat(60));
  console.log(`📊 RESULTS SUMMARY:`);
  console.log(`   Total Users: ${results.total}`);
  console.log(`   Login Success: ${results.loginSuccess} (${(results.loginSuccess/results.total*100).toFixed(1)}%)`);
  console.log(`   Login Failed: ${results.loginFailed} (${(results.loginFailed/results.total*100).toFixed(1)}%)`);

  if (enableWebSocket) {
    console.log(`   WebSocket Success: ${results.wsSuccess} (${results.loginSuccess > 0 ? (results.wsSuccess/results.loginSuccess*100).toFixed(1) : 0}%)`);
    console.log(`   WebSocket Failed: ${results.wsFailed} (${results.loginSuccess > 0 ? (results.wsFailed/results.loginSuccess*100).toFixed(1) : 0}%)`);
  }

  console.log(`\n⏱️  TIMING STATISTICS:`);
  console.log(`   Total Test Duration: ${(totalDuration/1000).toFixed(1)}s`);
  console.log(`   Average Login Time: ${avgLoginTime.toFixed(0)}ms`);

  if (enableWebSocket && avgWsTime > 0) {
    console.log(`   Average WebSocket Time: ${avgWsTime.toFixed(0)}ms`);
  }

  if (results.errors.length > 0) {
    console.log(`\n❌ ERRORS (${results.errors.length}):`);
    const errorCounts = {};
    results.errors.forEach(err => {
      const key = `${err.type}: ${err.error}`;
      errorCounts[key] = (errorCounts[key] || 0) + 1;
    });

    Object.entries(errorCounts).forEach(([error, count]) => {
      console.log(`   ${error} (${count}x)`);
    });
  }

  return results;
}

// Mass End-to-End Test (Login + Submit + Get Result)
async function runMassEndToEndTest(userCount = 50, options = {}) {
  console.log(`🚀 Starting Mass End-to-End Test for ${userCount} users`);
  console.log('='.repeat(70));

  const {
    batchSize = 10,           // Smaller batches for end-to-end testing
    batchDelay = 3000,        // Longer delay for processing
    userDelay = 200,          // Longer delay between users
    enableWebSocket = true,   // Whether to test WebSocket connections
    wsDelay = 300,           // Delay between WebSocket connections
    cleanupUsers = true,      // Whether to cleanup users after test
    highPerformance = false,  // High-performance mode
    skipProfile = false,      // Skip profile updates for speed
    jobTimeout = 300000       // 5 minutes timeout for job completion
  } = options;

  const startTime = Date.now();
  const results = {
    total: userCount,
    loginSuccess: 0,
    loginFailed: 0,
    assessmentSuccess: 0,
    assessmentFailed: 0,
    wsSuccess: 0,
    wsFailed: 0,
    jobSuccess: 0,
    jobFailed: 0,
    errors: [],
    durations: {
      login: [],
      assessment: [],
      websocket: [],
      jobCompletion: []
    }
  };

  // Check services health first
  console.log('\n📋 Checking services health...');
  const servicesHealthy = await testHealthCheck();
  if (!servicesHealthy) {
    console.log('\n❌ Some services are not healthy. Aborting end-to-end test.');
    return results;
  }

  const allSockets = [];
  const successfulUsers = [];
  const successfulAssessments = [];

  try {
    // Process users in batches
    for (let batchStart = 0; batchStart < userCount; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, userCount);
      const currentBatch = batchEnd - batchStart;

      console.log(`\n📦 Processing batch ${Math.floor(batchStart / batchSize) + 1}: Users ${batchStart + 1}-${batchEnd}`);

      // Step 1: Login users in current batch
      console.log(`   Step 1: Logging in ${currentBatch} users...`);
      const loginPromises = [];
      for (let i = batchStart; i < batchEnd; i++) {
        const delay = highPerformance ? 0 : (i - batchStart) * userDelay;
        loginPromises.push(loginSingleUser(i + 1, delay, skipProfile));
      }

      const loginResults = await Promise.all(loginPromises);

      // Process login results
      for (const result of loginResults) {
        if (result.success) {
          results.loginSuccess++;
          results.durations.login.push(result.duration);
          successfulUsers.push(result);
        } else {
          results.loginFailed++;
          results.errors.push({
            type: 'login',
            userIndex: result.userIndex,
            error: result.error
          });
        }
      }

      console.log(`   Login results: ${loginResults.filter(r => r.success).length}/${currentBatch} successful`);

      if (loginResults.filter(r => r.success).length === 0) {
        console.log(`   ⚠️ No successful logins in this batch, skipping further steps`);
        continue;
      }

      const successfulLogins = loginResults.filter(r => r.success);

      // Step 2: Connect WebSockets immediately after login (FIXED ORDER)
      if (enableWebSocket && successfulLogins.length > 0) {
        console.log(`   Step 2: Connecting WebSockets...`);

        const wsPromises = [];
        for (let i = 0; i < successfulLogins.length; i++) {
          const delay = i * wsDelay;
          wsPromises.push(connectWebSocketForUser(successfulLogins[i], delay));
        }

        const wsResults = await Promise.all(wsPromises);

        // Process WebSocket results
        for (const result of wsResults) {
          if (result.success) {
            results.wsSuccess++;
            results.durations.websocket.push(result.duration);
            allSockets.push(result.socket);
          } else {
            results.wsFailed++;
            results.errors.push({
              type: 'websocket',
              userIndex: result.userIndex,
              error: result.error
            });
          }
        }

        console.log(`   WebSocket results: ${wsResults.filter(r => r.success).length}/${successfulLogins.length} successful`);
      }

      // Step 3: Submit assessments for successful logins
      console.log(`   Step 3: Submitting assessments...`);
      const assessmentPromises = [];

      for (let i = 0; i < successfulLogins.length; i++) {
        const delay = highPerformance ? 0 : i * 500; // Longer delay for assessment submission
        assessmentPromises.push(submitAssessmentForUser(successfulLogins[i], delay));
      }

      const assessmentResults = await Promise.all(assessmentPromises);

      // Process assessment results
      for (const result of assessmentResults) {
        if (result.success) {
          results.assessmentSuccess++;
          results.durations.assessment.push(result.duration);
          successfulAssessments.push(result);
        } else {
          results.assessmentFailed++;
          results.errors.push({
            type: 'assessment',
            userIndex: result.userIndex,
            error: result.error
          });
        }
      }

      console.log(`   Assessment results: ${assessmentResults.filter(r => r.success).length}/${successfulLogins.length} successful`);

      // Delay before next batch (except for last batch)
      if (batchEnd < userCount) {
        console.log(`   Waiting ${batchDelay}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    }

    // Step 4: Wait for job completions
    if (successfulAssessments.length > 0) {
      console.log(`\n⏳ Step 4: Waiting for ${successfulAssessments.length} job completions...`);
      console.log(`   Timeout: ${jobTimeout / 1000}s per job`);

      const jobPromises = [];
      for (const assessmentResult of successfulAssessments) {
        jobPromises.push(waitForJobCompletion(assessmentResult, jobTimeout));
      }

      const jobResults = await Promise.all(jobPromises);

      // Process job completion results
      for (const result of jobResults) {
        if (result.success) {
          results.jobSuccess++;
          results.durations.jobCompletion.push(result.duration);
        } else {
          results.jobFailed++;
          results.errors.push({
            type: 'job_completion',
            userIndex: result.userIndex,
            error: result.error
          });
        }
      }

      console.log(`   Job completion results: ${results.jobSuccess}/${successfulAssessments.length} successful`);
    }

  } catch (error) {
    console.error('\n💥 End-to-end test execution failed:', error.message);
    results.errors.push({
      type: 'execution',
      error: error.message
    });
  } finally {
    // Cleanup WebSocket connections
    if (allSockets.length > 0) {
      console.log(`\n🧹 Disconnecting ${allSockets.length} WebSocket connections...`);
      for (const socket of allSockets) {
        try {
          socket.disconnect();
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    }

    // Cleanup user accounts if requested
    if (cleanupUsers && successfulUsers.length > 0) {
      console.log(`\n🗑️  Cleaning up ${successfulUsers.length} user accounts...`);
      let cleanupCount = 0;

      for (const userResult of successfulUsers) {
        try {
          const deleteResult = await makeRequest('DELETE', `${API_GATEWAY_URL}/api/auth/profile`, null, {
            'Authorization': `Bearer ${userResult.authToken}`
          });
          if (deleteResult.success) {
            cleanupCount++;
          }
        } catch (error) {
          // Ignore cleanup errors
        }
      }

      console.log(`   Cleaned up ${cleanupCount}/${successfulUsers.length} user accounts`);
    }
  }

  // Calculate statistics
  const endTime = Date.now();
  const totalDuration = endTime - startTime;

  const avgLoginTime = results.durations.login.length > 0
    ? results.durations.login.reduce((a, b) => a + b, 0) / results.durations.login.length
    : 0;

  const avgAssessmentTime = results.durations.assessment.length > 0
    ? results.durations.assessment.reduce((a, b) => a + b, 0) / results.durations.assessment.length
    : 0;

  const avgWsTime = results.durations.websocket.length > 0
    ? results.durations.websocket.reduce((a, b) => a + b, 0) / results.durations.websocket.length
    : 0;

  const avgJobTime = results.durations.jobCompletion.length > 0
    ? results.durations.jobCompletion.reduce((a, b) => a + b, 0) / results.durations.jobCompletion.length
    : 0;

  // Display final results
  console.log('\n' + '='.repeat(70));
  console.log('🎉 MASS END-TO-END TEST COMPLETED!');
  console.log('='.repeat(70));
  console.log(`📊 RESULTS SUMMARY:`);
  console.log(`   Total Users: ${results.total}`);
  console.log(`   Login Success: ${results.loginSuccess} (${(results.loginSuccess/results.total*100).toFixed(1)}%)`);
  console.log(`   Login Failed: ${results.loginFailed} (${(results.loginFailed/results.total*100).toFixed(1)}%)`);
  console.log(`   Assessment Success: ${results.assessmentSuccess} (${results.loginSuccess > 0 ? (results.assessmentSuccess/results.loginSuccess*100).toFixed(1) : 0}%)`);
  console.log(`   Assessment Failed: ${results.assessmentFailed} (${results.loginSuccess > 0 ? (results.assessmentFailed/results.loginSuccess*100).toFixed(1) : 0}%)`);

  if (enableWebSocket) {
    console.log(`   WebSocket Success: ${results.wsSuccess} (${results.loginSuccess > 0 ? (results.wsSuccess/results.loginSuccess*100).toFixed(1) : 0}%)`);
    console.log(`   WebSocket Failed: ${results.wsFailed} (${results.loginSuccess > 0 ? (results.wsFailed/results.loginSuccess*100).toFixed(1) : 0}%)`);
  }

  console.log(`   Job Success: ${results.jobSuccess} (${results.assessmentSuccess > 0 ? (results.jobSuccess/results.assessmentSuccess*100).toFixed(1) : 0}%)`);
  console.log(`   Job Failed: ${results.jobFailed} (${results.assessmentSuccess > 0 ? (results.jobFailed/results.assessmentSuccess*100).toFixed(1) : 0}%)`);

  console.log(`\n⏱️  TIMING STATISTICS:`);
  console.log(`   Total Test Duration: ${(totalDuration/1000).toFixed(1)}s`);
  console.log(`   Average Login Time: ${avgLoginTime.toFixed(0)}ms`);
  console.log(`   Average Assessment Time: ${avgAssessmentTime.toFixed(0)}ms`);

  if (enableWebSocket && avgWsTime > 0) {
    console.log(`   Average WebSocket Time: ${avgWsTime.toFixed(0)}ms`);
  }

  if (avgJobTime > 0) {
    console.log(`   Average Job Completion Time: ${(avgJobTime/1000).toFixed(1)}s`);
  }

  if (results.errors.length > 0) {
    console.log(`\n❌ ERRORS (${results.errors.length}):`);
    const errorCounts = {};
    results.errors.forEach(err => {
      const key = `${err.type}: ${err.error}`;
      errorCounts[key] = (errorCounts[key] || 0) + 1;
    });

    Object.entries(errorCounts).forEach(([error, count]) => {
      console.log(`   ${error} (${count}x)`);
    });
  }

  return results;
}

module.exports = {
  runTests,
  runWebSocketFlow,
  testHealthCheck,
  testRegister,
  testLogin,
  testGetProfile,
  testUpdateProfile,
  testDeleteProfile,
  testConnectWebSocket,
  testSubmitAssessment,
  testMonitorJobStatus,
  testGetJobFromArchive,
  cleanup,
  generateRandomEmail,
  regenerateTestUser,
  // Mass testing functions
  runMassLoginTest,
  runMassEndToEndTest,
  createTestUser,
  loginSingleUser,
  connectWebSocketForUser,
  submitAssessmentForUser,
  waitForJobCompletion
};
